import pywifi
from pywifi import const
import time
import json
import os

# Initialize WiFi interface
wifi = pywifi.PyWiFi()
iface = wifi.interfaces()[0]

def scan_networks():
    """Scan for available WiFi networks"""
    try:
        iface.scan()
        time.sleep(2)  # Wait for scan to complete
        results = iface.scan_results()
        
        # Get unique SSIDs
        ssids = []
        for network in results:
            if network.ssid and network.ssid not in ssids:
                ssids.append(network.ssid)
        
        return ssids
    except Exception as e:
        print(f"Error scanning networks: {e}")
        return []

def connect_to_wifi(ssid, password):
    """Connect to a WiFi network"""
    try:
        # Disconnect from current network
        iface.disconnect()
        time.sleep(1)
        
        # Create network profile
        profile = pywifi.Profile()
        profile.ssid = ssid
        profile.auth = const.AUTH_ALG_OPEN
        profile.akm.append(const.AKM_TYPE_WPA2PSK)
        profile.cipher = const.CIPHER_TYPE_CCMP
        profile.key = password
        
        # Remove all existing profiles and add new one
        iface.remove_all_network_profiles()
        tmp_profile = iface.add_network_profile(profile)
        
        # Connect to the network
        iface.connect(tmp_profile)
        time.sleep(5)  # Wait for connection
        
        # Check if connected
        return iface.status() == const.IFACE_CONNECTED
        
    except Exception as e:
        print(f"Error connecting to WiFi: {e}")
        return False

def save_favorite(ssid, password):
    """Save network credentials to favorites"""
    try:
        # Load existing favorites
        if os.path.exists("networks.json"):
            with open("networks.json", "r", encoding='utf-8') as f:
                data = json.load(f)
        else:
            data = {}
        
        # Add new network
        data[ssid] = password
        
        # Save back to file
        with open("networks.json", "w", encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
            
    except Exception as e:
        print(f"Error saving favorite: {e}")

def load_favorites():
    """Load saved network credentials"""
    try:
        if os.path.exists("networks.json"):
            with open("networks.json", "r", encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        print(f"Error loading favorites: {e}")
        return {}

def get_current_connection():
    """Get current WiFi connection status"""
    try:
        if iface.status() == const.IFACE_CONNECTED:
            # Try to get current network name
            profiles = iface.network_profiles()
            if profiles:
                return profiles[0].ssid
        return None
    except Exception as e:
        print(f"Error getting current connection: {e}")
        return None

def disconnect_wifi():
    """Disconnect from current WiFi network"""
    try:
        iface.disconnect()
        time.sleep(2)
        return True
    except Exception as e:
        print(f"Error disconnecting: {e}")
        return False
