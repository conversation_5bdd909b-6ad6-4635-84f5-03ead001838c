# 🧠 WiFi Manager

تطبيق ويب لإدارة شبكات الواي فاي باستخدام Python و Flask

## المميزات

- 📶 فحص الشبكات المتاحة
- 🔗 الاتصال بالشبكات
- ⭐ حفظ الشبكات المفضلة
- 🔄 تحديث الشبكات تلقائياً
- 📱 واجهة مستخدم سهلة الاستخدام
- 🌐 دعم اللغة العربية

## متطلبات التشغيل

- Python 3.7+
- Windows (مطلوب لمكتبة pywifi)
- صلاحيات المدير (Administrator)

## التثبيت

1. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

2. تشغيل التطبيق:
```bash
python app.py
```

3. فتح المتصفح والذهاب إلى:
```
http://localhost:5000
```

## الاستخدام

1. **فحص الشبكات**: اضغط على "تحديث الشبكات" لفحص الشبكات المتاحة
2. **الاتصال**: اختر شبكة وأدخل كلمة المرور واضغط "اتصال"
3. **المفضلة**: فعّل "حفظ في المفضلة" لحفظ الشبكة للاستخدام السريع
4. **الاتصال السريع**: استخدم "اتصال سريع" للشبكات المحفوظة

## ملاحظات مهمة

- يجب تشغيل التطبيق بصلاحيات المدير
- يعمل فقط على نظام Windows
- تأكد من تفعيل WiFi على الجهاز

## الملفات

- `app.py`: التطبيق الرئيسي (Flask)
- `wifi_manager.py`: وحدة إدارة الواي فاي
- `templates/index.html`: واجهة المستخدم
- `networks.json`: ملف حفظ الشبكات المفضلة (يتم إنشاؤه تلقائياً)

## استكشاف الأخطاء

إذا واجهت مشاكل:
1. تأكد من تشغيل التطبيق بصلاحيات المدير
2. تأكد من تثبيت جميع المتطلبات
3. تأكد من تفعيل WiFi على الجهاز
4. جرب إعادة تشغيل التطبيق
