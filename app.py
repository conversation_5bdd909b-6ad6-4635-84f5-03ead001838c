from flask import Flask, render_template, request, redirect, flash, jsonify
from wifi_manager import (
    scan_networks, 
    connect_to_wifi, 
    save_favorite, 
    load_favorites,
    get_current_connection,
    disconnect_wifi
)
import threading
import time

app = Flask(__name__)
app.secret_key = 'wifi_manager_secret_key'

# Global variables for connection status
connection_status = {"connected": False, "ssid": None, "message": ""}

@app.route("/")
def index():
    """Main page"""
    networks = scan_networks()
    favorites = load_favorites()
    current_connection = get_current_connection()
    
    return render_template(
        "index.html", 
        networks=networks, 
        favorites=favorites,
        current_connection=current_connection
    )

@app.route("/connect", methods=["POST"])
def connect():
    """Connect to WiFi network"""
    ssid = request.form.get("ssid")
    password = request.form.get("password")
    save_to_favorites = request.form.get("save_favorite") == "on"
    
    if not ssid:
        flash("❌ يرجى اختيار شبكة", "error")
        return redirect("/")
    
    # Start connection in background thread
    def connect_thread():
        global connection_status
        connection_status["message"] = f"🔄 جاري الاتصال بالشبكة {ssid}..."
        
        success = connect_to_wifi(ssid, password)
        
        if success:
            connection_status["connected"] = True
            connection_status["ssid"] = ssid
            connection_status["message"] = f"✅ تم الاتصال بالشبكة {ssid} بنجاح"
            
            if save_to_favorites:
                save_favorite(ssid, password)
        else:
            connection_status["connected"] = False
            connection_status["ssid"] = None
            connection_status["message"] = f"❌ فشل الاتصال بالشبكة {ssid}"
    
    thread = threading.Thread(target=connect_thread)
    thread.start()
    
    flash(f"🔄 جاري الاتصال بالشبكة {ssid}...", "info")
    return redirect("/")

@app.route("/disconnect", methods=["POST"])
def disconnect():
    """Disconnect from current WiFi"""
    success = disconnect_wifi()
    
    if success:
        flash("✅ تم قطع الاتصال", "success")
    else:
        flash("❌ فشل في قطع الاتصال", "error")
    
    return redirect("/")

@app.route("/status")
def status():
    """Get connection status (AJAX endpoint)"""
    current_connection = get_current_connection()
    return jsonify({
        "connected": current_connection is not None,
        "ssid": current_connection,
        "message": connection_status["message"]
    })

@app.route("/refresh_networks")
def refresh_networks():
    """Refresh available networks (AJAX endpoint)"""
    networks = scan_networks()
    return jsonify({"networks": networks})

@app.route("/favorites")
def favorites():
    """Show favorites page"""
    favorites = load_favorites()
    return render_template("favorites.html", favorites=favorites)

@app.route("/connect_favorite/<ssid>", methods=["POST"])
def connect_favorite(ssid):
    """Connect to a favorite network"""
    favorites = load_favorites()
    
    if ssid in favorites:
        password = favorites[ssid]
        
        # Start connection in background thread
        def connect_thread():
            global connection_status
            connection_status["message"] = f"🔄 جاري الاتصال بالشبكة {ssid}..."
            
            success = connect_to_wifi(ssid, password)
            
            if success:
                connection_status["connected"] = True
                connection_status["ssid"] = ssid
                connection_status["message"] = f"✅ تم الاتصال بالشبكة {ssid} بنجاح"
            else:
                connection_status["connected"] = False
                connection_status["ssid"] = None
                connection_status["message"] = f"❌ فشل الاتصال بالشبكة {ssid}"
        
        thread = threading.Thread(target=connect_thread)
        thread.start()
        
        flash(f"🔄 جاري الاتصال بالشبكة {ssid}...", "info")
    else:
        flash("❌ الشبكة غير موجودة في المفضلة", "error")
    
    return redirect("/")

if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=5000)
