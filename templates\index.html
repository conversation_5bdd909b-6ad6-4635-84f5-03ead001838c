<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 WiFi Manager</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .current-connection {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .connection-info {
            font-weight: bold;
            color: #28a745;
        }
        
        .disconnect-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.5em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .favorites-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .favorite-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .favorite-name {
            font-weight: bold;
            color: #333;
        }
        
        .btn-small {
            padding: 8px 15px;
            font-size: 14px;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-weight: bold;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .refresh-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 15px;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 WiFi Manager</h1>
            <p>إدارة شبكات الواي فاي بسهولة</p>
        </div>
        
        <div class="status-bar">
            {% if current_connection %}
            <div class="current-connection">
                <div class="connection-info">
                    🟢 متصل بالشبكة: {{ current_connection }}
                </div>
                <form method="post" action="/disconnect" style="display: inline;">
                    <button type="submit" class="disconnect-btn">قطع الاتصال</button>
                </form>
            </div>
            {% else %}
            <div class="connection-info" style="color: #dc3545;">
                🔴 غير متصل بأي شبكة
            </div>
            {% endif %}
            
            <div id="status-message"></div>
        </div>
        
        <div class="main-content">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'error' if category == 'error' else 'success' if category == 'success' else 'info' }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <!-- Connect to Network Section -->
            <div class="section">
                <h2>📶 الاتصال بشبكة جديدة</h2>
                
                <button class="refresh-btn" onclick="refreshNetworks()">🔄 تحديث الشبكات</button>
                <div class="loading" id="loading">جاري البحث عن الشبكات...</div>
                
                <form method="post" action="/connect">
                    <div class="form-group">
                        <label for="ssid">اختر الشبكة:</label>
                        <select name="ssid" id="ssid" required>
                            <option value="">-- اختر شبكة --</option>
                            {% for network in networks %}
                                <option value="{{ network }}">📡 {{ network }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">كلمة المرور:</label>
                        <input type="password" name="password" id="password" placeholder="أدخل كلمة المرور">
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" name="save_favorite" id="save_favorite">
                            <label for="save_favorite">حفظ في المفضلة</label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">🔗 اتصال</button>
                </form>
            </div>
            
            <!-- Favorites Section -->
            {% if favorites %}
            <div class="section">
                <h2>⭐ الشبكات المفضلة</h2>
                <div class="favorites-grid">
                    {% for ssid, password in favorites.items() %}
                    <div class="favorite-card">
                        <div class="favorite-name">📡 {{ ssid }}</div>
                        <form method="post" action="/connect_favorite/{{ ssid }}" style="display: inline;">
                            <button type="submit" class="btn btn-small">اتصال سريع</button>
                        </form>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
    
    <script>
        // Auto-refresh status every 3 seconds
        setInterval(function() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('status-message');
                    if (data.message) {
                        statusDiv.innerHTML = data.message;
                        statusDiv.style.display = 'block';
                    }
                });
        }, 3000);
        
        // Refresh networks function
        function refreshNetworks() {
            const loading = document.getElementById('loading');
            const select = document.getElementById('ssid');
            
            loading.style.display = 'block';
            
            fetch('/refresh_networks')
                .then(response => response.json())
                .then(data => {
                    // Clear existing options
                    select.innerHTML = '<option value="">-- اختر شبكة --</option>';
                    
                    // Add new networks
                    data.networks.forEach(network => {
                        const option = document.createElement('option');
                        option.value = network;
                        option.textContent = '📡 ' + network;
                        select.appendChild(option);
                    });
                    
                    loading.style.display = 'none';
                })
                .catch(error => {
                    console.error('Error:', error);
                    loading.style.display = 'none';
                });
        }
    </script>
</body>
</html>
